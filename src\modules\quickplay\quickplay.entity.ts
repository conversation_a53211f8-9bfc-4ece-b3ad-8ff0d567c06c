import { AbstractEntity } from '@common/abstract.entity';
import { UseDto } from '@decorators/use-dto.decorator';
import {
	Column,
	Entity,
	Index,
	PrimaryColumn,
} from 'typeorm';

import { QuickplayDto } from './dtos/quickplay.dto';

@Index('quickplay_qp_id_key', ['quickplayId'], { unique: true })
@Entity('quickplay', { schema: 'public' })
@UseDto(QuickplayDto)
export class QuickplayEntity extends AbstractEntity {
	@PrimaryColumn('character varying', { name: 'qp_id', unique: true, length: 50 })
	quickplayId!: string;

	@Column('character varying', { name: 'game_key', length: 50 })
	gameKey!: string;

	@Column('character varying', { name: 'platform', length: 20 })
	platform!: string;

	@Column('character varying', { name: 'device_id', length: 100 })
	deviceId!: string;

	@Column('character varying', { name: 'ip_address', length: 50 })
	ipAddress!: string;
}

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { QuickplayController } from './quickplay.controller';
import { QuickplayEntity } from './quickplay.entity';
import { QuickplayService } from './quickplay.service';

@Module({
	imports: [
		TypeOrmModule.forFeature([
			QuickplayEntity,
		]),
	],
	controllers: [QuickplayController],
	exports: [QuickplayService],
	providers: [QuickplayService],
})
export class QuickplayModule {}
